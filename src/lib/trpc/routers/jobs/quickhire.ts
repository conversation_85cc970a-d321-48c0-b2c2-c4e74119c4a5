import { TRPCError } from "@trpc/server";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, job, organization, trade } from "@/db/schema";
import { protectedProcedure } from "@/lib/trpc/procedures";
import { checkJobOwnership, requireAuth } from "@/lib/trpc/utils/permissions";

export const quickHireRouter = {
  findProfessionalsForQuickHire: protectedProcedure
    .input(
      z.object({
        tradeId: z.string(),
        maxDistance: z.number().optional().default(50),
      }),
    )
    .query(async ({ input }) => {
      // Check if the trade is eligible for quick hire
      const result = await db.query.trade.findFirst({
        where: eq(trade.id, input.tradeId),
      });

      if (!result || !result.availableForQuickHire) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "This trade is not available for quick hire",
        });
      }

      // Find organizations with the specified trade that have opted in to quick hire
      const organizations = await db.query.organization.findMany({
        where: and(
          eq(organization.tradeId, input.tradeId),
          eq(organization.acceptsQuickHire, true),
        ),
        with: {
          trade: true,
          address: true,
          memberships: {
            columns: {
              userId: true,
            },
          },
        },
        extras: {
          bidsCount: db
            .$count(bid, eq(bid.organizationId, organization.id))
            .as("bidsCount"),
        },
      });

      return organizations;
    }),

  quickHire: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        organizationId: z.string(),
        amount: z.number(),
        estimatedDuration: z.number(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to quick hire for this job
      const isOwner = await checkJobOwnership(ctx.userId, input.jobId);
      const hasPermission = ctx.role === "admin" || isOwner;
      requireAuth(
        hasPermission,
        "You don't have permission to quick hire for this job",
      );

      // Check if the job exists and is a quick hire job
      const result = await db.query.job.findFirst({
        where: eq(job.id, input.jobId),
        with: {
          tasks: true,
        },
      });

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      if (result.jobType !== "QUICK_HIRE") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "This job is not eligible for quick hire",
        });
      }

      // Check if the organization exists and accepts quick hire
      const org = await db.query.organization.findFirst({
        where: eq(organization.id, input.organizationId),
      });

      if (!org || !org.acceptsQuickHire) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "This organization does not accept quick hire",
        });
      }

      // Create a bid for the job
      await db
        .insert(bid)
        .values({
          name: `Quick Hire: ${job.name}`,
          jobId: input.jobId,
          organizationId: input.organizationId,
          amount: input.amount,
          description: "Quick hire job",
          estimatedDuration: input.estimatedDuration,
          status: "ACCEPTED", // Auto-accept the bid
        })
        .returning();

      // Update the job status to AWARDED
      await db
        .update(job)
        .set({
          status: "AWARDED",
        })
        .where(eq(job.id, input.jobId));

      return result;
    }),
};
