import { TRPCError } from "@trpc/server";
import { and, asc, desc, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import {
  address,
  bid,
  job,
  type Property,
  property,
  schedule,
} from "@/db/schema";
import { jobProcedures } from "@/lib/trpc/procedures";
import { calculateDistance, type Location } from "@/lib/trpc/utils/geo";
import {
  getOptimizedPublishedJobs,
  getOptimizedUserJobs,
} from "@/lib/trpc/utils/optimized-queries";
import { getUserOrganization } from "@/lib/trpc/utils/permissions";
import {
  withQueryCache,
  withQueryPerformanceMonitoring,
} from "@/lib/trpc/utils/query-performance";

export const listingRouter = {
  listForUser: jobProcedures.list
    .input(
      z
        .object({
          limit: z.number().min(1).max(100).default(20),
          cursor: z.string().optional(),
          includeCompleted: z.boolean().default(true),
        })
        .optional(),
    )
    .query(async ({ input, ctx }) => {
      const { limit = 20, cursor, includeCompleted = true } = input || {};

      return withQueryPerformanceMonitoring("jobs.listForUser", async () => {
        const cacheKey = `user-jobs:${ctx.userId}:${limit}:${cursor}:${includeCompleted}`;

        return withQueryCache(
          cacheKey,
          () => getOptimizedUserJobs(db, ctx.userId),
          60000, // 1 minute cache
        );
      });
    }),

  listPublished: jobProcedures.list
    .input(
      z
        .object({
          limit: z.number().min(1).max(50).default(20),
          cursor: z.string().optional(),
        })
        .optional(),
    )
    .query(async ({ input }) => {
      const { limit = 20, cursor } = input || {};

      return withQueryPerformanceMonitoring("jobs.listPublished", async () => {
        const cacheKey = `published-jobs:${limit}:${cursor || "first"}`;

        return withQueryCache(
          cacheKey,
          () => getOptimizedPublishedJobs(db, limit, cursor),
          30000, // 30 seconds cache for published jobs
        );
      });
    }),

  listActiveForOrganization: jobProcedures.list
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      const activeJobs = await db
        .select({
          job,
          property,
          address,
          bidsCount: db.$count(bid, eq(job.id, bid.jobId)),
        })
        .from(job)
        .leftJoin(property, eq(job.propertyId, property.id))
        .innerJoin(address, eq(property.addressId, address.id))
        .where(
          and(
            eq(job.status, "PUBLISHED"),
            eq(bid.organizationId, input.organizationId),
            eq(bid.status, "ACCEPTED"),
          ),
        )
        .orderBy(desc(job.createdAt));

      const jobs = activeJobs.map((job) => ({
        ...job.job,
        property: {
          ...(job.property as Property),
          address: job.address,
        },
        bidsCount: job.bidsCount,
      }));

      return jobs;
    }),

  listPublishedByDistance: jobProcedures.list
    .input(
      z.object({
        maxDistance: z.number().optional().default(50), // Default 50 miles
      }),
    )
    .query(async ({ input, ctx }) => {
      // Find the user's organization
      const userOrg = await getUserOrganization(ctx.userId);

      if (!userOrg || !userOrg.address?.location) {
        console.log("No organization or location found, returning no jobs");
        return [];
      }

      const orgLocation = userOrg.address.location as Location;

      // Get all published jobs
      const publishedJobs = await db
        .select({
          job,
          property,
          address,
          bidsCount: db.$count(bid, eq(job.id, bid.jobId)),
        })
        .from(job)
        .leftJoin(property, eq(job.propertyId, property.id))
        .innerJoin(address, eq(property.addressId, address.id))
        .where(eq(job.status, "PUBLISHED"))
        .orderBy(desc(job.createdAt));

      if (!publishedJobs) {
        throw new TRPCError({
          code: "NOT_FOUND",
        });
      }

      const jobs = publishedJobs.map((job) => ({
        ...job.job,
        property: {
          ...(job.property as Property),
          address: job.address,
        },
        bidsCount: job.bidsCount,
      }));

      // Filter out jobs without location data
      const jobsWithLocation = jobs.filter(
        (job) => job.property.address?.location,
      );

      // Calculate distance for each job and filter by max distance
      const jobsWithDistance = await Promise.all(
        jobsWithLocation.map(async (job) => {
          const jobLocation = job.property.address.location as Location;
          const distance = await calculateDistance(orgLocation, jobLocation);

          if (distance !== undefined) {
            return {
              ...job,
              distance,
            };
          }

          return null;
        }),
      );

      // Filter out null results and jobs beyond max distance
      return jobsWithDistance
        .filter(
          (job): job is typeof job & { distance: number } =>
            job !== null &&
            typeof job.distance === "number" &&
            job.distance <= input.maxDistance,
        )
        .sort((a, b) => a.distance - b.distance);
    }),

  listByProperty: jobProcedures.list
    .input(
      z.object({
        propertyId: z.string(),
      }),
    )
    .query(async ({ input }) => {
      // return await db.query.job.findMany({
      //   where: eq(job.propertyId, input.propertyId),
      //   orderBy: [desc(job.completedAt), desc(job.createdAt)],
      //   with: {
      //     property: {
      //       with: {
      //         address: true,
      //       },
      //     },
      //     tasks: true,
      //     bids: true,
      //   },
      // });

      const result = await db
        .select()
        .from(job)
        .leftJoin(property, eq(job.propertyId, property.id))
        .innerJoin(address, eq(property.addressId, address.id))
        .where(eq(job.propertyId, input.propertyId))
        .orderBy(desc(job.completedAt), desc(job.createdAt));

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      return result.map((result) => {
        return {
          ...result.job,
          property: {
            ...(result.property as Property),
            address: result.address,
          },
        };
      });
    }),

  listActiveForUser: jobProcedures.list.query(async ({ ctx }) => {
    // Find the user's organization
    const userOrg = await getUserOrganization(ctx.userId);

    if (!userOrg) {
      return [];
    }

    // Get active jobs for this organization
    return db.query.job.findMany({
      where: and(
        eq(job.status, "PUBLISHED"),
        eq(bid.organizationId, userOrg.id as string),
        eq(bid.status, "ACCEPTED"),
      ),
      with: {
        property: {
          with: {
            address: true,
          },
        },
      },
      orderBy: [desc(job.createdAt)],
    });
  }),

  listScheduledForOrganization: jobProcedures.list
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      const scheduledJobs = await db.query.job.findMany({
        where: and(
          eq(job.status, "AWARDED"),
          eq(bid.organizationId, input.organizationId),
          eq(bid.status, "ACCEPTED"),
        ),
        with: {
          property: {
            with: {
              address: true,
            },
          },
          schedules: true,
        },
        orderBy: [asc(schedule.proposedStartDate)],
        extras: {
          bidsCount: db.$count(bid, eq(bid.jobId, job.id)).as("bidsCount"),
        },
      });

      return scheduledJobs;
    }),
};
