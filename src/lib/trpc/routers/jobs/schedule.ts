import { TRPCError } from "@trpc/server";
import {
  and,
  desc,
  eq,
  gte,
  type InferSelectModel,
  lte,
  or,
} from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import {
  bid,
  type job,
  membership,
  organization,
  property,
  schedule,
  user,
} from "@/db/schema";
import { protectedProcedure } from "@/lib/trpc/procedures";
import {
  checkJobOwnership,
  checkOrganizationMembership,
  requireAuth,
} from "@/lib/trpc/utils/permissions";

export const scheduleRouter = {
  proposeSchedule: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        proposedStartDate: z.date(),
        proposedEndDate: z.date(),
        notes: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Find the accepted bid for this job
      const acceptedBid = await db.query.bid.findFirst({
        where: and(eq(bid.jobId, input.jobId), eq(bid.status, "ACCEPTED")),
      });

      if (!acceptedBid) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No accepted bid found for this job",
        });
      }

      // Check if user is part of the organization that won the bid
      const isMember = await checkOrganizationMembership(
        ctx.userId,
        acceptedBid.organizationId,
      );
      requireAuth(
        isMember,
        "You don't have permission to propose a schedule for this job",
      );

      // Check if a schedule already exists
      const existingSchedule = await db.query.schedule.findFirst({
        where: eq(schedule.jobId, input.jobId),
      });

      if (existingSchedule) {
        // Update existing schedule
        const updatedSchedule = await db
          .update(schedule)
          .set({
            proposedStartDate: input.proposedStartDate,
            proposedEndDate: input.proposedEndDate,
            notes: input.notes,
            status: "PROPOSED",
          })
          .where(eq(schedule.jobId, input.jobId))
          .returning();

        return updatedSchedule[0];
      }

      // Create new schedule
      const [newSchedule] = await db
        .insert(schedule)
        .values({
          jobId: input.jobId,
          proposedById: ctx.userId,
          proposedByRole: ctx.role || "contractor",
          proposedStartDate: input.proposedStartDate,
          proposedEndDate: input.proposedEndDate,
          notes: input.notes,
          status: "PROPOSED",
        })
        .returning();

      return newSchedule;
    }),

  confirmSchedule: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        confirmedStartDate: z.date(),
        confirmedEndDate: z.date(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to confirm schedule for this job
      const isOwner = await checkJobOwnership(ctx.userId, input.jobId);
      const hasPermission = ctx.role === "admin" || isOwner;
      requireAuth(
        hasPermission,
        "You don't have permission to confirm the schedule for this job",
      );

      // Check if a schedule already exists
      const existingSchedule = await db.query.schedule.findFirst({
        where: eq(schedule.jobId, input.jobId),
      });

      if (!existingSchedule) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No proposed schedule found for this job",
        });
      }

      // Update the schedule
      const [updatedSchedule] = await db
        .update(schedule)
        .set({
          confirmedStartDate: input.confirmedStartDate,
          confirmedEndDate: input.confirmedEndDate,
          status: "CONFIRMED",
        })
        .where(eq(schedule.jobId, input.jobId))
        .returning();

      return updatedSchedule;
    }),

  getSchedules: protectedProcedure
    .input(z.object({ jobId: z.string() }))
    .query(async ({ input }) => {
      return await db.query.schedule.findMany({
        where: eq(schedule.jobId, input.jobId),
        orderBy: [desc(schedule.createdAt)],
      });
    }),

  // Keep the existing getSchedule for backward compatibility
  getSchedule: protectedProcedure
    .input(z.object({ jobId: z.string() }))
    .query(async ({ input }) => {
      return await db.query.schedule.findFirst({
        where: eq(schedule.jobId, input.jobId),
        orderBy: [desc(schedule.createdAt)],
      });
    }),

  listScheduledJobs: protectedProcedure
    .input(
      z.object({
        startDate: z.date(),
        endDate: z.date(),
      }),
    )
    .query(async ({ input, ctx }) => {
      // Find all jobs where the user is either the property owner or part of the organization that won the bid
      const userAccount = await db.query.user.findFirst({
        where: eq(user.id, ctx.userId),
      });

      if (!userAccount) {
        return [];
      }

      // Find user's organization if they're a professional
      const userOrg = await db.query.organization.findFirst({
        where: eq(
          organization.id,
          db
            .select({ id: membership.organizationId })
            .from(membership)
            .where(eq(membership.userId, ctx.userId)),
        ),
      });

      // Build the query based on user role
      let scheduledJobs: InferSelectModel<typeof job>[] = [];

      // Common schedule date filter - now checks any schedule that overlaps with the date range
      const dateFilter = or(
        and(
          gte(schedule.confirmedStartDate, input.startDate),
          lte(schedule.confirmedStartDate, input.endDate),
        ),
        and(
          gte(schedule.confirmedEndDate, input.startDate),
          lte(schedule.confirmedEndDate, input.endDate),
        ),
        and(
          lte(schedule.confirmedStartDate, input.startDate),
          gte(schedule.confirmedEndDate, input.endDate),
        ),
      );

      // If homeowner, filter by properties they own
      if (userAccount.role === "HOMEOWNER") {
        scheduledJobs = await db.query.job.findMany({
          where: and(eq(property.userId, userAccount.id), dateFilter),
          with: {
            property: {
              with: {
                address: true,
              },
            },
            schedules: true,
            bids: {
              where: eq(bid.status, "ACCEPTED"),
              with: {
                organization: true,
              },
            },
          },
        });
      }

      // If professional, filter by jobs their organization won
      if (userAccount.role === "PROFESSIONAL" && userOrg) {
        scheduledJobs = await db.query.job.findMany({
          where: and(
            eq(bid.organizationId, userOrg.id),
            eq(bid.status, "ACCEPTED"),
            dateFilter,
          ),
          with: {
            property: {
              with: {
                address: true,
              },
            },
            schedules: true,
            bids: {
              where: eq(bid.status, "ACCEPTED"),
              with: {
                organization: true,
              },
            },
          },
        });
      }

      return scheduledJobs;
    }),
};
