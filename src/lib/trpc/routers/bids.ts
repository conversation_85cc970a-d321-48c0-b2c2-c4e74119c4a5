import { TRPCError } from "@trpc/server";
import { and, desc, eq, ne } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, job } from "@/db/schema";
import { sendBidNotificationEmail } from "@/lib/email/send-bid-notification";
import {
  bidProcedures,
  protectedProcedure,
  router,
} from "@/lib/trpc/procedures";
import { bidCreateSchema, entityInputSchema } from "../schemas";
import { getBidWithRelations, getJobWithRelations } from "../utils/entities";
import { calculateDistance, type Location } from "../utils/geo";
import {
  checkJobOwnership,
  checkOrganizationMembership,
  getUserOrganization,
  requireAuth,
} from "../utils/permissions";

export const bidsRouter = router({
  listForOrganization: protectedProcedure.query(async ({ ctx }) => {
    // Find the user's organization
    const userOrg = await getUserOrganization(ctx.userId);

    if (!userOrg) {
      return [];
    }

    // Get bids for this organization
    const bidList = await db.query.bid.findMany({
      where: eq(bid.organizationId, userOrg.id as string),
      with: {
        job: {
          with: {
            property: {
              with: {
                address: true,
              },
            },
          },
        },
        organization: {
          with: {
            address: true,
          },
        },
      },
      orderBy: [desc(bid.createdAt)],
      extras: {
        jobCount: db.$count(job, eq(bid.jobId, job.id)).as("jobCount"),
      },
    });

    // Calculate distances for each bid where possible
    const bidsWithDistance = await Promise.all(
      bidList.map(async (bid) => {
        let distance: number | undefined;

        if (
          bid.job.property.address?.location &&
          bid.organization.address?.location
        ) {
          const propertyLocation = bid.job.property.address
            .location as Location;
          const contractorLocation = bid.organization.address
            .location as Location;

          distance = await calculateDistance(
            propertyLocation,
            contractorLocation,
          );
        }

        return {
          ...bid,
          distance,
        };
      }),
    );

    return bidsWithDistance;
  }),

  create: protectedProcedure
    .input(bidCreateSchema)
    .mutation(async ({ input, ctx }) => {
      // Check if the user is part of the organization
      const isMember = await checkOrganizationMembership(
        ctx.userId,
        input.organizationId,
      );
      requireAuth(isMember, "You are not a member of this organization");

      // Check if the job exists and is published
      const job = await getJobWithRelations(input.jobId);

      if (job.status !== "PUBLISHED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot bid on a job that is not published",
        });
      }

      // Check if the organization has already bid on this job
      const existingBid = await db.query.bid.findFirst({
        where: and(
          eq(bid.jobId, input.jobId),
          eq(bid.organizationId, input.organizationId),
        ),
      });

      if (existingBid) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Your organization has already submitted a bid for this job",
        });
      }

      // Create the bid
      const [newBid] = await db
        .insert(bid)
        .values({
          name: input.name,
          jobId: input.jobId,
          organizationId: input.organizationId,
          amount: input.amount,
          description: input.description,
          estimatedDuration: input.estimatedDuration,
        })
        .returning();

      if (!newBid) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create bid",
        });
      }

      // Send notification email to property owner
      await sendBidNotificationEmail(newBid.id);

      return newBid;
    }),

  getById: bidProcedures.read
    .input(entityInputSchema)
    .query(async ({ input, ctx }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You must be logged in to view bids",
        });
      }

      const bid = await getBidWithRelations(input.id);

      // Check if user has permission to view this bid
      const isPropertyOwner = await checkJobOwnership(ctx.userId, bid.jobId);
      const isBidder = await checkOrganizationMembership(
        ctx.userId,
        bid.organizationId,
      );
      const hasAdminAccess = ctx.role === "admin";

      requireAuth(
        hasAdminAccess || isPropertyOwner || isBidder,
        "You don't have permission to view this bid",
      );

      // Calculate distance if both addresses have location data
      let distance: number | undefined;

      if (
        bid.job.property.address.location &&
        bid.organization.address.location
      ) {
        const propertyLocation = bid.job.property.address.location as Location;
        const contractorLocation = bid.organization.address
          .location as Location;

        distance = await calculateDistance(
          propertyLocation,
          contractorLocation,
        );
      }

      return {
        ...bid,
        distance,
      };
    }),

  accept: protectedProcedure
    .input(entityInputSchema)
    .mutation(async ({ input, ctx }) => {
      const b = await getBidWithRelations(input.id);

      // Check if user has permission to accept this bid
      const isPropertyOwner = await checkJobOwnership(ctx.userId, b.jobId);
      const hasPermission = ctx.role === "admin" || isPropertyOwner;
      requireAuth(
        hasPermission,
        "You don't have permission to accept this bid",
      );

      // Update the bid status to ACCEPTED
      const [updatedBid] = await db
        .update(bid)
        .set({
          status: "ACCEPTED",
        })
        .where(eq(bid.id, input.id))
        .returning();

      // Update the job status to AWARDED
      await db
        .update(job)
        .set({
          status: "AWARDED",
        })
        .where(eq(job.id, bid.jobId));

      // Reject all other bids for this job
      await db
        .update(bid)
        .set({
          status: "REJECTED",
        })
        .where(and(eq(bid.jobId, bid.jobId), ne(bid.id, input.id)));

      return updatedBid;
    }),

  edit: protectedProcedure
    .input(
      z.object({
        id: z.string().min(1, "ID is required"),
        name: z.string().min(1, "Name is required").optional(),
        amount: z
          .number()
          .min(0.01, "Amount must be greater than 0")
          .optional(),
        description: z
          .string()
          .min(10, "Please provide a detailed description")
          .optional(),
        estimatedDuration: z
          .number()
          .min(1, "Estimated duration is required")
          .optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Check if the bid exists
      const b = await getBidWithRelations(input.id);

      // Check if the user is part of the organization that created the bid
      const isMember = await checkOrganizationMembership(
        ctx.userId,
        b.organizationId,
      );
      requireAuth(isMember, "You don't have permission to edit this bid");

      // Check if the bid is in a state that can be edited
      if (b.status !== "PROPOSED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Only proposed bids can be edited",
        });
      }

      // Update the bid with the provided fields
      const updatedBid = await db
        .update(bid)
        .set({
          ...(input.name && { name: input.name }),
          ...(input.amount !== undefined && { amount: input.amount }),
          ...(input.description && { description: input.description }),
          ...(input.estimatedDuration !== undefined && {
            estimatedDuration: input.estimatedDuration,
          }),
        })
        .where(eq(bid.id, input.id));

      return updatedBid;
    }),

  withdraw: protectedProcedure
    .input(entityInputSchema)
    .mutation(async ({ input, ctx }) => {
      // Check if the bid exists
      const b = await getBidWithRelations(input.id);

      // Check if the user is part of the organization that created the bid
      const isMember = await checkOrganizationMembership(
        ctx.userId,
        b.organizationId,
      );
      requireAuth(isMember, "You don't have permission to withdraw this bid");

      // Check if the bid is in a state that can be withdrawn
      if (b.status !== "PROPOSED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Only proposed bids can be withdrawn",
        });
      }

      // Update the bid status to WITHDRAWN
      const updatedBid = await db
        .update(bid)
        .set({
          status: "WITHDRAWN",
        })
        .where(eq(bid.id, input.id));

      return updatedBid;
    }),
});
